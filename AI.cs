using System.Collections.Generic;
using UnityEngine;

namespace ColdVor.RTS
{
    /// <summary>
    /// AI state enumeration for state machine behavior
    /// </summary>
    public enum AIState
    {
        Idle,           // Not doing anything
        Patrol,         // Patrolling assigned area
        Investigate,    // Investigating point of interest
        Engage,         // Engaging enemy targets
        Assault,        // Aggressive attack mode
        Retreat,        // Withdrawing from combat
        Defend,         // Holding defensive position
        Support         // Supporting other units
    }

    /// <summary>
    /// Generalized AI Controller for all autonomous units (friendly and enemy)
    /// Handles decision trees, tactical behavior, patrol routes, and combat engagement
    /// </summary>
    [RequireComponent(typeof(Unit))]
    public class AI : MonoBehaviour
    {
        [Header("AI Behavior")]
        [SerializeField] private AIState initialState = AIState.Patrol;
        [SerializeField] private float stateUpdateInterval = 0.5f;
        // decisionMakingRange now uses Unit.DetectionRange

        [Header("Debug")]
        [SerializeField] private bool enableDetailedLogging = false;

        [Header("Patrol Settings")]
        [SerializeField] private Transform[] patrolPoints;
        [SerializeField] private bool randomPatrol = false;
        [SerializeField] private float randomPatrolRadius = 15f;

        [Header("Combat Settings")]
        [SerializeField] private float optimalFireRange = 15f;
        // minimumFireRange removed - use Weapon.MinimumRange from WeaponSystem
        [SerializeField] private float pursuitRange = 25f;
        [SerializeField] private float retreatHealthThreshold = 0.25f;

        [Header("Group Behavior")]
        [SerializeField] private bool useGroupBehavior = true;
        // groupCommunicationRange now uses Unit.CommunicationRange
        [SerializeField] private float reinforcementCallRange = 30f;

        [Header("Suppression & Morale")]
        [SerializeField] private float suppressionDecayRate = 10f; // Per second
        [SerializeField] private float moraleRecoveryRate = 5f; // Per second
        [SerializeField] private float maxSuppressionLevel = 100f;
        [SerializeField] private float maxMoraleLevel = 100f;

        // State machine
        private AIState currentState = AIState.Idle;
        private AIState previousState = AIState.Idle;
        private float stateEnterTime;
        private float lastStateUpdate;

        // Patrol state
        private int currentPatrolIndex = 0;
        private Vector3 randomPatrolCenter;
        private float lastPatrolMoveTime = 0f;
        private float patrolMoveInterval = 3f; // Wait 3 seconds between patrol moves

        // Combat state
        private Unit primaryTarget;
        private Vector3 lastKnownEnemyPosition;
        private float lastEnemyContactTime;
        private float suppressionLevel = 0f;
        private float moraleLevel = 100f;

        // Movement and positioning
        private Vector3 homePosition;
        public Unit unit; // Made public for system access

        // Public properties for TacticalAwareness to access AI configuration
        public float RandomPatrolRadius => randomPatrolRadius;
        public float OptimalFireRange => optimalFireRange;
        public bool RandomPatrol => randomPatrol;
        // MinimumFireRange removed - TacticalAwareness should use unit.WeaponSystem.CurrentWeapon.MinimumRange

        // Staggered updates for performance
        private float updateOffset;
        private float lastSuppressionUpdate;
        private float lastGroupCoordinationUpdate;

        // Detection and awareness
        private List<Unit> knownEnemies = new List<Unit>();

        // Group coordination
        private List<AI> nearbyAllies = new List<AI>();
        private bool hasCalledForReinforcements = false;
        
        // Tactical decision tracking
        private bool isExecutingTacticalDecision = false;
        private float tacticalDecisionEndTime = 0f;

        // Events
        public System.Action<AI, AIState, AIState> OnStateChanged;
        public System.Action<AI, Unit> OnEnemyEngaged;
        public System.Action<AI> OnRetreatStarted;

        // Properties
        public AIState CurrentState => currentState;
        public Unit PrimaryTarget => primaryTarget;
        public Vector3 HomePosition => homePosition;
        public float LastReinforcementCall { get; private set; } = -30f;

        private void Awake()
        {
            unit = GetComponent<Unit>();
            if (unit == null)
            {
                Debug.LogError($"AI on {gameObject.name} could not find Unit component!");
                return;
            }

            homePosition = transform.position;
            randomPatrolCenter = homePosition;

            // Initialize staggered update system
            InitializeStaggeredUpdates();

            // Subscribe to unit events
            unit.OnEnemyDetected += OnEnemyDetected;
            unit.OnEnemyLost += OnEnemyLost;
            unit.OnHealthChanged += OnHealthChanged;

            // Subscribe to tactical awareness events
            if (TacticalAwareness.Instance != null)
            {
                TacticalAwareness.Instance.OnTacticalDecisionMade += OnTacticalDecisionMade;
            }
        }

        private void Start()
        {
            // Validate NavMeshAgent
            if (unit != null && unit.NavAgent == null)
            {
                Debug.LogError($"AI on {gameObject.name}: Unit has no NavMeshAgent!");
                return;
            }

            ChangeState(initialState);

            // Initialize random patrol center if using random patrol
            if (randomPatrol && patrolPoints == null || patrolPoints.Length == 0)
            {
                randomPatrolCenter = homePosition;
            }
        }

        /// <summary>
        /// Initialize staggered update system to spread AI processing across frames
        /// </summary>
        private void InitializeStaggeredUpdates()
        {
            // Spread AI updates across frames based on instance ID
            updateOffset = (GetInstanceID() % 10) * 0.1f;
            lastStateUpdate = Time.time + updateOffset;
            lastSuppressionUpdate = Time.time + updateOffset;
            lastGroupCoordinationUpdate = Time.time + updateOffset;
        }

        private void OnDestroy()
        {
            // Unsubscribe from events
            if (unit != null)
            {
                unit.OnEnemyDetected -= OnEnemyDetected;
                unit.OnEnemyLost -= OnEnemyLost;
                unit.OnHealthChanged -= OnHealthChanged;
            }

            if (TacticalAwareness.Instance != null)
            {
                TacticalAwareness.Instance.OnTacticalDecisionMade -= OnTacticalDecisionMade;
            }
        }

        private void Update()
        {
            // Don't update AI if unit is dead
            if (unit == null || unit.IsDead)
            {
                return;
            }

            // Update suppression and morale (lightweight, every frame)
            UpdateSuppressionAndMorale();

            // Staggered state machine updates
            if (Time.time - lastStateUpdate >= stateUpdateInterval)
            {
                UpdateStateMachine();
                lastStateUpdate = Time.time;
            }

            // Update current state behavior (lightweight, every frame)
            UpdateCurrentState();

            // Staggered group coordination updates (heavier operation)
            if (useGroupBehavior && Time.time - lastGroupCoordinationUpdate >= (stateUpdateInterval * 0.5f))
            {
                UpdateGroupCoordination();
                lastGroupCoordinationUpdate = Time.time;
            }
        }

        #region State Machine

        /// <summary>
        /// Main state machine update - determines next state based on current situation
        /// </summary>
        private void UpdateStateMachine()
        {
            AIState newState = DetermineNextState();

            if (newState != currentState)
            {
                ChangeState(newState);
            }

            // Debug logging
            if (DebugManager.Instance != null)
            {
                DebugManager.Instance.LogAI($"{unit.name}: State={currentState}, Enemies={unit.DetectedEnemies.Count}, Target={primaryTarget?.name ?? "null"}");
            }
        }

        /// <summary>
        /// Determine the next state - TacticalAwareness has FULL authority
        /// </summary>
        private AIState DetermineNextState()
        {
            // TACTICAL AWARENESS HAS FULL AUTHORITY - AI is pure executor
            if (isExecutingTacticalDecision && Time.time < tacticalDecisionEndTime)
            {
                // TacticalAwareness is in control - maintain current state
                return currentState;
            }

            // Only maintain current state when TacticalAwareness isn't active
            // TacticalAwareness handles ALL decision making including health, suppression, targets
            return currentState;
        }

        /// <summary>
        /// Determine tactical state based on enemy analysis and unit capabilities
        /// </summary>
        private AIState DetermineTacticalState(Unit enemy, float distance)
        {
            if (enemy == null) return currentState;

            // Analyze tactical situation
            bool hasAdvantage = AnalyzeTacticalAdvantage(enemy);
            bool hasSupport = nearbyAllies.Count > 0;
            bool enemyIsWounded = enemy.CurrentHealth < enemy.MaxHealth * 0.7f;

            // Decision tree based on tactical analysis
            if (distance <= optimalFireRange)
            {
                if (hasAdvantage || enemyIsWounded)
                {
                    return AIState.Engage;
                }
                else if (!hasSupport && suppressionLevel > 30f)
                {
                    return AIState.Retreat;
                }
                else
                {
                    return AIState.Engage;
                }
            }
            else if (distance <= pursuitRange)
            {
                if (hasAdvantage && hasSupport)
                {
                    return AIState.Assault;
                }
                else
                {
                    return AIState.Engage;
                }
            }
            else
            {
                return AIState.Investigate;
            }
        }

        /// <summary>
        /// Analyze tactical advantage against an enemy
        /// </summary>
        private bool AnalyzeTacticalAdvantage(Unit enemy)
        {
            if (enemy == null) return false;

            // Health advantage
            float ourHealthRatio = (float)unit.CurrentHealth / unit.MaxHealth;
            float enemyHealthRatio = (float)enemy.CurrentHealth / enemy.MaxHealth;
            bool healthAdvantage = ourHealthRatio > enemyHealthRatio + 0.2f;

            // Numerical advantage
            bool numericalAdvantage = nearbyAllies.Count > 0;

            // Position advantage (higher ground, cover, etc.)
            bool positionAdvantage = transform.position.y > enemy.transform.position.y + 2f;

            // Weapon range advantage
            bool rangeAdvantage = unit.WeaponRange > enemy.WeaponRange;

            // Return true if we have multiple advantages
            int advantages = 0;
            if (healthAdvantage) advantages++;
            if (numericalAdvantage) advantages++;
            if (positionAdvantage) advantages++;
            if (rangeAdvantage) advantages++;

            return advantages >= 2;
        }

        /// <summary>
        /// Change to a new AI state
        /// </summary>
        private void ChangeState(AIState newState)
        {
            if (newState == currentState) return;

            // Exit current state
            ExitState(currentState);

            // Store previous state
            previousState = currentState;
            currentState = newState;
            stateEnterTime = Time.time;

            // Enter new state
            EnterState(newState);

            // Trigger state change event
            OnStateChanged?.Invoke(this, previousState, currentState);

            // Debug logging
            if (enableDetailedLogging)
            {
                DebugManager.Instance?.LogAI($"{unit.name}: {previousState} → {newState}");
            }
        }

        private void EnterState(AIState state)
        {
            switch (state)
            {
                case AIState.Idle:
                    unit.StopMovement();
                    break;

                case AIState.Patrol:
                    hasCalledForReinforcements = false;
                    if (patrolPoints != null && patrolPoints.Length > 0)
                    {
                        MoveToNextPatrolPoint();
                    }
                    // TacticalAwareness handles all intelligent movement decisions
                    break;

                case AIState.Investigate:
                    if (lastKnownEnemyPosition != Vector3.zero)
                    {
                        unit.MoveTo(lastKnownEnemyPosition);
                    }
                    break;

                case AIState.Engage:
                    // Engagement logic is now handled by TacticalAwareness
                    // This ensures tactical decisions cannot be overridden
                    if (primaryTarget == null)
                    {
                        Unit target = unit.GetClosestEnemy();
                        if (target != null)
                        {
                            SetPrimaryTarget(target);
                        }
                        else
                        {
                            ChangeState(AIState.Patrol);
                            return;
                        }
                    }

                    OnEnemyEngaged?.Invoke(this, primaryTarget);

                    // Call for reinforcements if enabled
                    if (useGroupBehavior && !hasCalledForReinforcements)
                    {
                        CallForReinforcements();
                    }
                    break;

                case AIState.Retreat:
                    OnRetreatStarted?.Invoke(this);
                    Vector3 retreatPosition = CalculateRetreatPosition();
                    unit.MoveTo(retreatPosition);
                    break;
            }
        }

        private void ExitState(AIState state)
        {
            switch (state)
            {
                case AIState.Engage:
                    SetPrimaryTarget(null);
                    break;
            }
        }

        /// <summary>
        /// Apply suppression to this AI unit
        /// </summary>
        public void ApplySuppression(float amount)
        {
            suppressionLevel = Mathf.Min(maxSuppressionLevel, suppressionLevel + amount);

            // Reduce morale when suppressed
            moraleLevel = Mathf.Max(0f, moraleLevel - amount * 0.5f);

            if (DebugManager.Instance != null)
            {
                DebugManager.Instance.LogAISuppression(this, suppressionLevel, moraleLevel);
            }
        }

        /// <summary>
        /// Set primary target for engagement - works WITH Unit's auto-engagement
        /// </summary>
        private void SetPrimaryTarget(Unit target)
        {
            primaryTarget = target;

            // AI doesn't override Unit's targeting - it just tracks what it's focusing on
            // Unit's auto-engagement will handle the actual targeting and firing

            if (target != null)
            {
                lastKnownEnemyPosition = target.transform.position;
                lastEnemyContactTime = Time.time;
            }
        }

        /// <summary>
        /// Get closest enemy within specified range
        /// </summary>
        private Unit GetClosestEnemyInRange(float range)
        {
            Unit closestEnemy = null;
            float closestDistance = range;

            foreach (Unit enemy in unit.DetectedEnemies)
            {
                if (enemy == null || enemy.IsDead) continue;

                float distance = Vector3.Distance(transform.position, enemy.transform.position);
                if (distance < closestDistance)
                {
                    closestDistance = distance;
                    closestEnemy = enemy;
                }
            }

            return closestEnemy;
        }

        /// <summary>
        /// Update suppression and morale levels
        /// </summary>
        private void UpdateSuppressionAndMorale()
        {
            // Decay suppression over time
            if (suppressionLevel > 0f)
            {
                suppressionLevel = Mathf.Max(0f, suppressionLevel - suppressionDecayRate * Time.deltaTime);
            }

            // Recover morale when not suppressed
            if (suppressionLevel < 20f && moraleLevel < maxMoraleLevel)
            {
                moraleLevel = Mathf.Min(maxMoraleLevel, moraleLevel + moraleRecoveryRate * Time.deltaTime);
            }
        }

        /// <summary>
        /// Update current state behavior
        /// </summary>
        private void UpdateCurrentState()
        {
            // Check if we're executing a tactical decision - if so, don't override it
            if (isExecutingTacticalDecision && Time.time < tacticalDecisionEndTime)
            {
                return; // Let tactical decision complete
            }
            else if (isExecutingTacticalDecision && Time.time >= tacticalDecisionEndTime)
            {
                isExecutingTacticalDecision = false; // Tactical decision completed
            }

            switch (currentState)
            {
                case AIState.Engage:
                    UpdateEngageState();
                    break;
                case AIState.Patrol:
                    UpdatePatrolState();
                    break;
                case AIState.Investigate:
                    UpdateInvestigateState();
                    break;
                case AIState.Retreat:
                    UpdateRetreatState();
                    break;
            }
        }

        /// <summary>
        /// Update engage state - TacticalAwareness handles ALL engagement tactics
        /// </summary>
        private void UpdateEngageState()
        {
            if (primaryTarget == null || primaryTarget.IsDead)
            {
                // Target lost or dead - state machine will handle finding new targets
                return;
            }

            // TacticalAwareness handles ALL engagement positioning and tactics
            // AI just maintains the target and state
        }

        // All positioning calculation methods removed - TacticalAwareness handles ALL tactical positioning

        /// <summary>
        /// Update patrol state - TacticalAwareness handles ALL movement decisions
        /// </summary>
        private void UpdatePatrolState()
        {
            // TacticalAwareness handles ALL patrol movement - no fallback logic
            // AI is pure executor - only responds to TacticalAwareness decisions
        }

        /// <summary>
        /// Move to next patrol point
        /// </summary>
        private void MoveToNextPatrolPoint()
        {
            if (patrolPoints == null || patrolPoints.Length == 0) return;

            currentPatrolIndex = (currentPatrolIndex + 1) % patrolPoints.Length;
            if (patrolPoints[currentPatrolIndex] != null)
            {
                unit.MoveTo(patrolPoints[currentPatrolIndex].position);
            }
        }

        // StartBasicPatrol removed - TacticalAwareness handles ALL patrol decisions

        // StartRandomPatrol removed - TacticalAwareness handles ALL patrol movement decisions

        /// <summary>
        /// Update investigate state
        /// </summary>
        private void UpdateInvestigateState()
        {
            if (!unit.IsMoving)
            {
                // Reached investigation point - return to patrol
                ChangeState(AIState.Patrol);
            }
        }

        /// <summary>
        /// Update retreat state
        /// </summary>
        private void UpdateRetreatState()
        {
            if (!unit.IsMoving)
            {
                // Check if we can return to combat
                float healthPercentage = (float)unit.CurrentHealth / unit.MaxHealth;
                if (healthPercentage > retreatHealthThreshold + 0.1f && suppressionLevel < 30f)
                {
                    ChangeState(AIState.Patrol);
                }
            }
        }

        /// <summary>
        /// Calculate retreat position away from threats
        /// </summary>
        private Vector3 CalculateRetreatPosition()
        {
            Vector3 retreatDirection = Vector3.zero;
            int threatCount = 0;

            // Move away from all known enemies
            foreach (Unit enemy in unit.DetectedEnemies)
            {
                if (enemy != null && !enemy.IsDead)
                {
                    Vector3 awayFromEnemy = transform.position - enemy.transform.position;
                    retreatDirection += awayFromEnemy.normalized;
                    threatCount++;
                }
            }

            if (threatCount > 0)
            {
                retreatDirection /= threatCount;
            }
            else
            {
                // No specific threats - retreat towards home
                retreatDirection = (homePosition - transform.position).normalized;
            }

            return transform.position + retreatDirection * 20f;
        }

        /// <summary>
        /// Update group coordination
        /// </summary>
        private void UpdateGroupCoordination()
        {
            if (!useGroupBehavior) return;

            // Find nearby allies
            nearbyAllies.Clear();
            Collider[] nearbyColliders = Physics.OverlapSphere(transform.position, unit.CommunicationRange);

            foreach (Collider col in nearbyColliders)
            {
                AI allyAI = col.GetComponent<AI>();
                if (allyAI != null && allyAI != this && allyAI.unit.Faction == unit.Faction)
                {
                    nearbyAllies.Add(allyAI);
                }
            }
        }

        /// <summary>
        /// Call for reinforcements
        /// </summary>
        private void CallForReinforcements()
        {
            if (hasCalledForReinforcements) return;

            hasCalledForReinforcements = true;
            LastReinforcementCall = Time.time;

            // Notify command hierarchy
            if (CommandHierarchy.Instance != null)
            {
                CommandHierarchy.Instance.RequestReinforcements(unit, transform.position, reinforcementCallRange);
            }
        }

        /// <summary>
        /// Handle tactical decision from TacticalAwareness
        /// </summary>
        private void OnTacticalDecisionMade(TacticalDecision decision)
        {
            // Only respond to decisions for this unit
            if (decision.unit != unit) return;

            if (DebugManager.Instance != null)
            {
                DebugManager.Instance.LogAI($"AI {unit.name}: Received tactical decision {decision.decisionType}");
            }

            // Set tactical decision execution flag - TacticalAwareness has authority
            isExecutingTacticalDecision = true;
            tacticalDecisionEndTime = Time.time + 5f; // Give tactical decision 5 seconds to execute

            // Execute tactical decision based on type
            switch (decision.decisionType)
            {
                case TacticalDecisionType.Engage:
                    if (decision.targetUnit != null)
                    {
                        SetPrimaryTarget(decision.targetUnit);
                        ChangeState(AIState.Engage);
                    }
                    break;

                case TacticalDecisionType.Retreat:
                    ChangeState(AIState.Retreat);
                    break;

                case TacticalDecisionType.Reposition:
                    if (decision.targetPosition != Vector3.zero)
                    {
                        unit.MoveTo(decision.targetPosition);
                        // Maintain engagement state during repositioning if we have a target
                        if (primaryTarget != null)
                        {
                            ChangeState(AIState.Engage);
                        }
                    }
                    break;

                case TacticalDecisionType.Defend:
                    unit.StopMovement();
                    ChangeState(AIState.Patrol); // Defensive stance
                    break;

                case TacticalDecisionType.Patrol:
                    if (decision.targetPosition != Vector3.zero)
                    {
                        unit.MoveTo(decision.targetPosition);
                    }
                    ChangeState(AIState.Patrol);
                    break;
            }
        }

        /// <summary>
        /// Event handlers
        /// </summary>
        private void OnEnemyDetected(Unit detector, Unit enemy)
        {
            if (detector != unit) return;

            if (!knownEnemies.Contains(enemy))
            {
                knownEnemies.Add(enemy);
            }

            lastEnemyContactTime = Time.time;
            lastKnownEnemyPosition = enemy.transform.position;

            // Immediately engage if we can attack
            if (unit.CanAttack && enemy != null)
            {
                SetPrimaryTarget(enemy);
                ChangeState(AIState.Engage);

                if (DebugManager.Instance != null)
                {
                    DebugManager.Instance.LogAI($"{unit.name}: Enemy detected - engaging {enemy.name}");
                }
            }
            else if (currentState == AIState.Patrol || currentState == AIState.Idle)
            {
                ChangeState(AIState.Investigate);
            }
        }

        private void OnEnemyLost(Unit detector, Unit enemy)
        {
            if (detector != unit) return;

            knownEnemies.Remove(enemy);
        }

        private void OnHealthChanged(Unit changedUnit, int newHealth)
        {
            if (changedUnit != unit) return;

            // Check if we need to retreat due to low health
            float healthPercentage = (float)newHealth / unit.MaxHealth;
            if (healthPercentage <= retreatHealthThreshold && currentState != AIState.Retreat)
            {
                ChangeState(AIState.Retreat);
            }
        }

        /// <summary>
        /// Investigate a specific position
        /// </summary>
        public void InvestigatePosition(Vector3 position)
        {
            lastKnownEnemyPosition = position;
            ChangeState(AIState.Investigate);
            unit.MoveTo(position);
        }

        /// <summary>
        /// Engage a specific target
        /// </summary>
        public void EngageTarget(Unit target)
        {
            if (target != null)
            {
                SetPrimaryTarget(target);
                ChangeState(AIState.Engage);
            }
        }

        /// <summary>
        /// Respond to reinforcement call
        /// </summary>
        public void RespondToReinforcement(Vector3 position, Unit targetUnit = null)
        {
            // Move towards reinforcement position
            unit.MoveTo(position);

            // If a specific unit needs support, set it as target
            if (targetUnit != null)
            {
                SetPrimaryTarget(targetUnit);
                ChangeState(AIState.Engage);
            }
            else
            {
                ChangeState(AIState.Support);
            }
        }

        #endregion
    }
}
