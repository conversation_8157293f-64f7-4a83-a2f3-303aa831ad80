using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace ColdVor.RTS
{
    /// <summary>
    /// Consolidated tactical awareness and vision system
    /// Handles vision, threat assessment, tactical decision making, and autonomous combat behavior
    /// </summary>
    public class TacticalAwareness : SingletonBase<TacticalAwareness>
    {
        [Header("Vision & Detection")]
        [SerializeField] private bool enableFogOfWar = true;
        [SerializeField] private float fogUpdateInterval = 0.1f; // More frequent updates for smoother fog
        [SerializeField] private LayerMask visionBlockingLayers = 1;
        [SerializeField] private int fogResolution = 512; // Higher resolution for better fog quality
        [SerializeField] private bool useHeightAdvantage = true;
        [SerializeField] private float heightAdvantageMultiplier = 1.5f;

        [Header("Awareness Settings")]
        [SerializeField] private float awarenessUpdateInterval = 1f;
        [SerializeField] private float situationAssessmentRange = 30f;
        [SerializeField] private bool enablePredictiveAnalysis = true;
        [SerializeField] private float predictionTimeHorizon = 5f;

        [Header("Combat Autonomy")]
        [SerializeField] private bool enableCombatAutonomy = true;
        [SerializeField] private float retreatHealthThreshold = 0.3f; // Retreat when below 30% health
        [SerializeField] private float repositionCooldown = 5f;
        [SerializeField] private float coverSeekingRange = 15f;
        [SerializeField] private bool enableSuppressionSystem = true;

        [Header("Decision Making")]
        [SerializeField] private float decisionConfidenceThreshold = 0.4f;
        [SerializeField] private bool enableGroupDecisions = true;
        [SerializeField] private float groupDecisionRadius = 25f;
        [SerializeField] private int maxConcurrentDecisions = 5;
        [SerializeField] private bool enablePlayerUnitDecisions = true; // Allow decisions for player units

        [Header("Performance Settings")]
        [SerializeField] private bool enableStaggeredUpdates = true;
        [SerializeField] private int unitsPerFrameBatch = 5;

        [Header("Tactical Responses")]
        [SerializeField] private bool enableAutomaticResponses = true;
        [SerializeField] private float responseDelay = 0.5f;
        [SerializeField] private float coordinationBonus = 0.2f;

        // Vision system data
        private Dictionary<Unit, VisionData> unitVisionData = new Dictionary<Unit, VisionData>();
        private Texture2D fogTexture;
        private Color[] fogPixels;
        private Vector2 mapCenter;
        private Vector2 mapSize = new Vector2(100f, 100f);
        private float lastFogUpdate;
        private List<Unit> playerUnits = new List<Unit>();

        // Awareness data
        private Dictionary<Unit, TacticalSituation> unitSituations = new Dictionary<Unit, TacticalSituation>();
        private Dictionary<Faction, FactionAwareness> factionAwareness = new Dictionary<Faction, FactionAwareness>();
        private List<TacticalDecision> activeDecisions = new List<TacticalDecision>();
        private Dictionary<Unit, float> lastRepositionTime = new Dictionary<Unit, float>();

        // Update tracking
        private float lastAwarenessUpdate;
        private List<Unit> allUnits = new List<Unit>();

        // Staggered update system for units
        private int currentUnitBatch = 0;
        private float lastVisionBatchUpdate;

        // Events
        public System.Action<Unit, TacticalSituation> OnSituationAssessed;
        public System.Action<TacticalDecision> OnTacticalDecisionMade;
        public System.Action<Unit, TacticalResponse> OnTacticalResponseExecuted;
        public System.Action<Unit, Unit> OnUnitSpotted;
        public System.Action<Unit, Unit> OnUnitLostSight;
        public System.Action<Vector2> OnFogOfWarUpdated;

        protected override void OnSingletonAwake()
        {
            InitializeAwareness();
            InitializeFogOfWar();
        }

        private void Start()
        {
            RefreshUnitList();
            SubscribeToEvents();
        }

        private void Update()
        {
            // Update fog of war periodically
            if (enableFogOfWar && Time.time - lastFogUpdate >= fogUpdateInterval)
            {
                UpdateFogOfWar();
                lastFogUpdate = Time.time;
            }

            // Update vision data using staggered batches or immediate updates
            if (enableStaggeredUpdates)
            {
                UpdateVisionDataStaggered();
            }
            else
            {
                UpdateVisionData();
            }

            ProcessTacticalDecisions();

            // Process combat autonomy
            if (enableCombatAutonomy)
            {
                ProcessCombatAutonomy();
                // Note: Player units now use the same targeting system as AI units (handled in Unit.cs)
            }

            // Update tactical awareness at its own interval
            if (Time.time - lastAwarenessUpdate >= awarenessUpdateInterval)
            {
                UpdateTacticalAwareness();
                lastAwarenessUpdate = Time.time;
            }
        }

        #region Vision System

        private void InitializeFogOfWar()
        {
            if (!enableFogOfWar) return;

            fogTexture = new Texture2D(fogResolution, fogResolution, TextureFormat.RGBA32, false);
            fogPixels = new Color[fogResolution * fogResolution];

            // Initialize fog to semi-transparent black (unexplored)
            for (int i = 0; i < fogPixels.Length; i++)
            {
                fogPixels[i] = new Color(0, 0, 0, 0.8f); // Semi-transparent black
            }

            fogTexture.SetPixels(fogPixels);
            fogTexture.Apply();

            // Create fog of war plane if it doesn't exist
            CreateFogOfWarPlane();
        }

        private void CreateFogOfWarPlane()
        {
            GameObject fogPlane = GameObject.Find("FogOfWarPlane");
            if (fogPlane == null)
            {
                fogPlane = GameObject.CreatePrimitive(PrimitiveType.Plane);
                fogPlane.name = "FogOfWarPlane";
                fogPlane.transform.position = new Vector3(0, 0.1f, 0); // Slightly above ground
                fogPlane.transform.localScale = new Vector3(20, 1, 20); // Large plane

                // Remove collider so it doesn't interfere with gameplay
                Collider collider = fogPlane.GetComponent<Collider>();
                if (collider != null) Destroy(collider);

                // Create fog material
                Material fogMaterial = new Material(Shader.Find("Unlit/Transparent"));
                fogMaterial.mainTexture = fogTexture;
                fogMaterial.color = new Color(1, 1, 1, 0.7f); // Semi-transparent

                Renderer renderer = fogPlane.GetComponent<Renderer>();
                if (renderer != null)
                {
                    renderer.material = fogMaterial;
                    renderer.sortingOrder = 1000; // Render on top
                }
            }
        }

        /// <summary>
        /// Update vision data using staggered batches to distribute load across frames
        /// </summary>
        private void UpdateVisionDataStaggered()
        {
            if (allUnits.Count == 0) return;

            // Use configurable batch size
            int batchSize = Mathf.Max(1, unitsPerFrameBatch);

            // Process a batch of units each frame
            int startIndex = currentUnitBatch * batchSize;
            int endIndex = Mathf.Min(startIndex + batchSize, allUnits.Count);

            for (int i = startIndex; i < endIndex; i++)
            {
                Unit unit = allUnits[i];
                if (unit == null || !unit.gameObject.activeInHierarchy || unit.IsDead) continue;
                UpdateUnitVision(unit);
            }

            // Move to next batch
            currentUnitBatch++;
            if (currentUnitBatch * batchSize >= allUnits.Count)
            {
                currentUnitBatch = 0; // Reset to start
            }
        }

        /// <summary>
        /// Legacy method for immediate vision updates when needed
        /// </summary>
        private void UpdateVisionData()
        {
            foreach (Unit unit in allUnits)
            {
                if (unit == null || !unit.gameObject.activeInHierarchy || unit.IsDead) continue;
                UpdateUnitVision(unit);
            }
        }

        /// <summary>
        /// Force immediate vision update for all units (use sparingly)
        /// </summary>
        public void ForceImmediateVisionUpdate()
        {
            UpdateVisionData();
        }

        /// <summary>
        /// Force immediate vision update for a specific unit
        /// </summary>
        public void ForceImmediateVisionUpdate(Unit unit)
        {
            if (unit != null && !unit.IsDead)
            {
                UpdateUnitVision(unit);
            }
        }

        private void UpdateUnitVision(Unit unit)
        {
            if (!unitVisionData.ContainsKey(unit))
            {
                unitVisionData[unit] = new VisionData();
            }

            VisionData visionData = unitVisionData[unit];
            List<Unit> previouslyVisible = new List<Unit>(visionData.visibleUnits);
            visionData.visibleUnits.Clear();

            float visionRange = GetUnitVisionRange(unit);

            // Check all other units for visibility
            foreach (Unit otherUnit in allUnits)
            {
                if (otherUnit == null || otherUnit == unit || !otherUnit.gameObject.activeInHierarchy || otherUnit.IsDead) continue;

                if (CanUnitSeeUnit(unit, otherUnit, visionRange))
                {
                    visionData.visibleUnits.Add(otherUnit);

                    // Trigger spotted event for new sightings
                    if (!previouslyVisible.Contains(otherUnit))
                    {
                        OnUnitSpotted?.Invoke(unit, otherUnit);
                    }
                }
            }

            // Trigger lost sight events
            foreach (Unit lostUnit in previouslyVisible)
            {
                if (!visionData.visibleUnits.Contains(lostUnit))
                {
                    OnUnitLostSight?.Invoke(unit, lostUnit);
                }
            }
        }

        private bool CanUnitSeeUnit(Unit observer, Unit target, float visionRange)
        {
            Vector3 observerPos = observer.transform.position + Vector3.up * 1.5f;
            Vector3 targetPos = target.transform.position + Vector3.up * 1f;

            float distance = Vector3.Distance(observerPos, targetPos);
            if (distance > visionRange) return false;

            // Check field of view
            Vector3 directionToTarget = (targetPos - observerPos).normalized;
            Vector3 observerForward = observer.transform.forward;
            float angle = Vector3.Angle(observerForward, directionToTarget);

            if (angle > observer.FieldOfViewAngle * 0.5f) return false;

            // Check line of sight
            return HasLineOfSight(observerPos, targetPos);
        }

        private bool HasLineOfSight(Vector3 from, Vector3 to)
        {
            Vector3 direction = (to - from).normalized;
            float distance = Vector3.Distance(from, to);

            return !Physics.Raycast(from, direction, distance, visionBlockingLayers);
        }

        private float GetUnitVisionRange(Unit unit)
        {
            // Use the unit's vision range
            float baseRange = unit.VisionRange;

            if (useHeightAdvantage)
            {
                float height = unit.transform.position.y;
                if (height > 5f) // Height advantage threshold
                {
                    baseRange *= heightAdvantageMultiplier;
                }
            }

            // Don't artificially limit vision - respect the unit's set range
            return baseRange;
        }

        private void UpdateFogOfWar()
        {
            if (!enableFogOfWar || fogTexture == null) return;

            // Fade fog gradually
            for (int i = 0; i < fogPixels.Length; i++)
            {
                fogPixels[i] = Color.Lerp(fogPixels[i], Color.black, Time.deltaTime * 2f);
            }

            // Reveal areas around player units
            foreach (Unit unit in playerUnits)
            {
                if (unit != null && unit.gameObject.activeInHierarchy && !unit.IsDead)
                {
                    RevealAreaAroundUnit(unit);
                }
            }

            fogTexture.SetPixels(fogPixels);
            fogTexture.Apply();
            OnFogOfWarUpdated?.Invoke(mapCenter);
        }

        private void RevealAreaAroundUnit(Unit unit)
        {
            Vector3 unitPos = unit.transform.position;
            float visionRange = GetUnitVisionRange(unit);

            // Use larger map size for better fog coverage
            Vector2 mapSizeForFog = new Vector2(200f, 200f); // Larger map coverage
            Vector2 mapCenterForFog = new Vector2(0f, 0f); // Centered at origin

            int centerX = Mathf.RoundToInt((unitPos.x - mapCenterForFog.x + mapSizeForFog.x * 0.5f) / mapSizeForFog.x * fogResolution);
            int centerY = Mathf.RoundToInt((unitPos.z - mapCenterForFog.y + mapSizeForFog.y * 0.5f) / mapSizeForFog.y * fogResolution);
            int radius = Mathf.RoundToInt(visionRange / mapSizeForFog.x * fogResolution);

            for (int x = centerX - radius; x <= centerX + radius; x++)
            {
                for (int y = centerY - radius; y <= centerY + radius; y++)
                {
                    if (x >= 0 && x < fogResolution && y >= 0 && y < fogResolution)
                    {
                        float distance = Vector2.Distance(new Vector2(x, y), new Vector2(centerX, centerY));
                        if (distance <= radius)
                        {
                            int index = y * fogResolution + x;
                            float alpha = 1f - (distance / radius);
                            // Make revealed areas more transparent (less fog)
                            Color revealedColor = new Color(1f, 1f, 1f, 0.1f); // Almost transparent
                            fogPixels[index] = Color.Lerp(fogPixels[index], revealedColor, alpha * 0.8f);
                        }
                    }
                }
            }
        }

        #endregion

        #region Awareness System

        private void InitializeAwareness()
        {
            Faction[] allFactions = { Faction.Player, Faction.Enemy, Faction.Neutral, Faction.Ally };
            foreach (Faction faction in allFactions)
            {
                factionAwareness[faction] = new FactionAwareness
                {
                    faction = faction,
                    overallThreatLevel = ThreatLevel.None,
                    knownEnemies = new List<Unit>(),
                    tacticalAdvantage = 0f
                };
            }
        }

        private void UpdateTacticalAwareness()
        {
            RefreshUnitList();

            if (DebugManager.Instance != null && allUnits.Count > 0)
            {
                DebugManager.Instance.LogSystem("TacticalAwareness", $"Updating awareness for {allUnits.Count} units");
            }

            // Update individual unit situations
            foreach (Unit unit in allUnits)
            {
                if (unit == null || !unit.gameObject.activeInHierarchy) continue;
                UpdateUnitSituation(unit);
            }

            // Update faction-level awareness
            Faction[] allFactions = { Faction.Player, Faction.Enemy, Faction.Neutral, Faction.Ally };
            foreach (Faction faction in allFactions)
            {
                UpdateFactionAwareness(faction);
            }

            // Generate tactical decisions
            if (enableAutomaticResponses)
            {
                GenerateAdvancedTacticalDecisions();
            }
        }

        private void UpdateUnitSituation(Unit unit)
        {
            if (!unitSituations.ContainsKey(unit))
            {
                unitSituations[unit] = new TacticalSituation { unit = unit };
            }

            TacticalSituation situation = unitSituations[unit];

            // Assess immediate threats
            AssessImmediateThreats(unit, situation);

            // Assess tactical position
            AssessTacticalPosition(unit, situation);

            // Assess support availability
            AssessSupportAvailability(unit, situation);

            // Calculate overall situation assessment
            CalculateSituationScore(situation);

            // Predict future developments if enabled
            if (enablePredictiveAnalysis)
            {
                PredictFutureSituation(unit, situation);
            }

            OnSituationAssessed?.Invoke(unit, situation);
        }

        private void AssessImmediateThreats(Unit unit, TacticalSituation situation)
        {
            situation.immediateThreats.Clear();
            situation.threatLevel = ThreatLevel.None;

            float highestThreat = 0f;

            foreach (Unit enemy in unit.DetectedEnemies)
            {
                if (enemy == null) continue;

                float distance = Vector3.Distance(unit.transform.position, enemy.transform.position);
                float threatScore = CalculateAdvancedThreatScore(unit, enemy, distance);

                if (threatScore > 0.1f)
                {
                    ThreatInfo threat = new ThreatInfo
                    {
                        threatUnit = enemy,
                        threatScore = threatScore,
                        distance = distance,
                        lastSeen = Time.time,
                        threatType = DetermineThreatType(enemy),
                        coverAvailable = AssessCoverAvailability(unit, enemy),
                        predictedPosition = PredictEnemyPosition(enemy, 2f)
                    };

                    situation.immediateThreats.Add(threat);
                    highestThreat = Mathf.Max(highestThreat, threatScore);
                }
            }

            // Determine threat level
            if (highestThreat >= 0.8f) situation.threatLevel = ThreatLevel.Critical;
            else if (highestThreat >= 0.6f) situation.threatLevel = ThreatLevel.High;
            else if (highestThreat >= 0.4f) situation.threatLevel = ThreatLevel.Medium;
            else if (highestThreat >= 0.2f) situation.threatLevel = ThreatLevel.Low;
            else situation.threatLevel = ThreatLevel.None;
        }

        private void AssessTacticalPosition(Unit unit, TacticalSituation situation)
        {
            // Assess cover, elevation, flanking opportunities, etc.
            situation.hasGoodPosition = EvaluatePosition(unit);
            situation.canRetreat = CanRetreatSafely(unit);
            situation.flankingOpportunities = FindFlankingOpportunities(unit);
        }

        private void AssessSupportAvailability(Unit unit, TacticalSituation situation)
        {
            situation.nearbyAllies.Clear();
            situation.availableSupport = 0f;

            foreach (Unit ally in unit.DetectedFriendlies)
            {
                if (ally == null) continue;

                float distance = Vector3.Distance(unit.transform.position, ally.transform.position);
                if (distance <= situationAssessmentRange)
                {
                    situation.nearbyAllies.Add(ally);
                    situation.availableSupport += CalculateSupportValue(ally, distance);
                }
            }
        }

        private void CalculateSituationScore(TacticalSituation situation)
        {
            float score = 0.5f; // Neutral starting point

            // Factor in threat level
            switch (situation.threatLevel)
            {
                case ThreatLevel.Critical: score -= 0.4f; break;
                case ThreatLevel.High: score -= 0.3f; break;
                case ThreatLevel.Medium: score -= 0.2f; break;
                case ThreatLevel.Low: score -= 0.1f; break;
            }

            // Factor in position quality
            if (situation.hasGoodPosition) score += 0.2f;
            if (situation.canRetreat) score += 0.1f;

            // Factor in support
            score += Mathf.Min(situation.availableSupport * 0.1f, 0.3f);

            // Factor in flanking opportunities
            score += situation.flankingOpportunities.Count * 0.05f;

            situation.overallScore = Mathf.Clamp01(score);
        }

        private void PredictFutureSituation(Unit unit, TacticalSituation situation)
        {
            // Simple prediction based on current movement and threat trajectories
            Vector3 predictedPosition = unit.transform.position + unit.NavAgent.velocity * predictionTimeHorizon;

            // Predict threat changes
            foreach (var threat in situation.immediateThreats)
            {
                if (threat.threatUnit != null && threat.threatUnit.NavAgent != null)
                {
                    Vector3 threatPredictedPos = threat.threatUnit.transform.position +
                                               threat.threatUnit.NavAgent.velocity * predictionTimeHorizon;

                    float predictedDistance = Vector3.Distance(predictedPosition, threatPredictedPos);
                    threat.predictedThreatScore = CalculateThreatScore(unit, threat.threatUnit, predictedDistance);
                }
            }
        }

        #endregion

        #region Decision Making

        private void GenerateAdvancedTacticalDecisions()
        {
            // Limit concurrent decisions
            if (activeDecisions.Count >= maxConcurrentDecisions) return;

            foreach (Unit unit in allUnits)
            {
                if (unit == null || !unit.gameObject.activeInHierarchy) continue;

                // Skip player units only if player unit decisions are disabled
                if (unit.Faction == Faction.Player && !enablePlayerUnitDecisions) continue;

                if (unitSituations.ContainsKey(unit))
                {
                    TacticalSituation situation = unitSituations[unit];

                    TacticalDecision decision = null;

                    // Check if group decisions are enabled and unit is in a group
                    if (enableGroupDecisions && IsUnitInGroup(unit))
                    {
                        decision = GenerateGroupDecisions(unit, situation);
                    }
                    else
                    {
                        // Generate advanced tactical decisions based on situation
                        decision = GenerateAdvancedDecision(unit, situation);
                    }

                    if (decision != null && decision.priority > 0.3f)
                    {
                        decision.unit = unit;
                        if (DebugManager.Instance != null)
                        {
                            DebugManager.Instance.LogAI($"TacticalAwareness: Generated decision {decision.decisionType} for {unit.name} (Priority: {decision.priority:F2}, Confidence: {decision.confidence:F2})");
                        }
                        ExecuteTacticalDecision(decision);
                        activeDecisions.Add(decision);
                    }
                }
            }
        }

        private TacticalDecision GenerateAdvancedDecision(Unit unit, TacticalSituation situation)
        {
            if (unit == null || situation == null) return null;

            // DECISION TREE: Hierarchical tactical decision making
            return ExecuteDecisionTree(unit, situation);
        }

        /// <summary>
        /// Execute decision tree for tactical analysis
        /// </summary>
        private TacticalDecision ExecuteDecisionTree(Unit unit, TacticalSituation situation)
        {
            // ROOT NODE: Health Assessment
            float healthPercentage = (float)unit.CurrentHealth / unit.MaxHealth;

            // BRANCH 1: Critical Health (< 30%) -> Retreat Decision Tree
            if (healthPercentage < 0.3f)
            {
                return ProcessRetreatDecisionTree(unit, situation);
            }

            // BRANCH 2: Threat Assessment
            if (situation.immediateThreats.Count > 0)
            {
                return ProcessThreatDecisionTree(unit, situation);
            }

            // BRANCH 3: Opportunity Assessment
            return ProcessOpportunityDecisionTree(unit, situation);
        }

        /// <summary>
        /// Decision tree for retreat scenarios
        /// </summary>
        private TacticalDecision ProcessRetreatDecisionTree(Unit unit, TacticalSituation situation)
        {
            // Can we retreat safely?
            if (situation.canRetreat)
            {
                return new TacticalDecision
                {
                    decisionType = TacticalDecisionType.Retreat,
                    priority = 0.9f,
                    confidence = 0.8f,
                    targetPosition = CalculateSafeRetreatPosition(unit),
                    reasoning = "Critical health - tactical retreat"
                };
            }

            // Cannot retreat - fight desperately
            return new TacticalDecision
            {
                decisionType = TacticalDecisionType.Engage,
                priority = 0.7f,
                confidence = 0.6f,
                reasoning = "Cannot retreat - desperate engagement"
            };
        }

        /// <summary>
        /// Decision tree for threat response
        /// </summary>
        private TacticalDecision ProcessThreatDecisionTree(Unit unit, TacticalSituation situation)
        {
            var highestThreat = situation.immediateThreats
                .OrderByDescending(t => t.threatScore)
                .FirstOrDefault();

            if (highestThreat == null) return null;

            // High threat level -> Tactical response required
            if (highestThreat.threatScore > 0.7f)
            {
                // Do we have support?
                if (situation.nearbyAllies.Count >= 2)
                {
                    // Coordinated assault
                    return new TacticalDecision
                    {
                        decisionType = TacticalDecisionType.Engage,
                        priority = 0.8f,
                        confidence = 0.7f,
                        targetUnit = highestThreat.threatUnit,
                        reasoning = "High threat - coordinated response"
                    };
                }
                else
                {
                    // Tactical repositioning
                    return new TacticalDecision
                    {
                        decisionType = TacticalDecisionType.Reposition,
                        priority = 0.6f,
                        confidence = 0.8f,
                        targetPosition = CalculateTacticalPosition(unit, highestThreat.threatUnit),
                        reasoning = "High threat - tactical repositioning"
                    };
                }
            }

            // Medium threat -> Standard engagement
            return new TacticalDecision
            {
                decisionType = TacticalDecisionType.Engage,
                priority = 0.5f,
                confidence = 0.6f,
                targetUnit = highestThreat.threatUnit,
                reasoning = "Medium threat - standard engagement"
            };
        }

        /// <summary>
        /// Decision tree for opportunity assessment
        /// </summary>
        private TacticalDecision ProcessOpportunityDecisionTree(Unit unit, TacticalSituation situation)
        {
            // No immediate threats - look for opportunities

            // Check for tactical advantages
            if (situation.hasPositionalAdvantage && situation.nearbyAllies.Count > 0)
            {
                return new TacticalDecision
                {
                    decisionType = TacticalDecisionType.Defend,
                    priority = 0.4f,
                    confidence = 0.7f,
                    reasoning = "Positional advantage - defensive stance"
                };
            }

            // AUTONOMOUS BEHAVIOR: Generate basic movement decisions
            return GenerateAutonomousDecision(unit, situation);
        }

        /// <summary>
        /// Generate autonomous behavior decisions for basic AI functionality
        /// </summary>
        private TacticalDecision GenerateAutonomousDecision(Unit unit, TacticalSituation situation)
        {
            // Check if unit has detected enemies but no immediate threats
            if (unit.DetectedEnemies.Count > 0)
            {
                Unit closestEnemy = GetClosestDetectedEnemy(unit);
                if (closestEnemy != null)
                {
                    float distanceToEnemy = Vector3.Distance(unit.transform.position, closestEnemy.transform.position);

                    // If enemy is within weapon range, engage
                    if (distanceToEnemy <= unit.WeaponRange)
                    {
                        return new TacticalDecision
                        {
                            decisionType = TacticalDecisionType.Engage,
                            targetUnit = closestEnemy,
                            priority = 0.6f,
                            confidence = 0.8f,
                            reasoning = "Enemy in weapon range - engage"
                        };
                    }
                    // If enemy is beyond weapon range but within detection, advance
                    else if (distanceToEnemy <= unit.DetectionRange)
                    {
                        Vector3 advancePosition = CalculateAdvancePosition(unit, closestEnemy);
                        return new TacticalDecision
                        {
                            decisionType = TacticalDecisionType.Reposition,
                            targetPosition = advancePosition,
                            priority = 0.5f,
                            confidence = 0.7f,
                            reasoning = "Enemy detected - advance to engagement range"
                        };
                    }
                }
            }

            // No enemies detected - generate patrol movement
            return GeneratePatrolDecision(unit, situation);
        }

        /// <summary>
        /// Get closest detected enemy for a unit
        /// </summary>
        private Unit GetClosestDetectedEnemy(Unit unit)
        {
            Unit closest = null;
            float closestDistance = float.MaxValue;

            foreach (Unit enemy in unit.DetectedEnemies)
            {
                if (enemy == null || enemy.IsDead) continue;

                float distance = Vector3.Distance(unit.transform.position, enemy.transform.position);
                if (distance < closestDistance)
                {
                    closestDistance = distance;
                    closest = enemy;
                }
            }

            return closest;
        }

        /// <summary>
        /// Calculate advance position toward enemy
        /// </summary>
        private Vector3 CalculateAdvancePosition(Unit unit, Unit enemy)
        {
            Vector3 directionToEnemy = (enemy.transform.position - unit.transform.position).normalized;
            float advanceDistance = unit.WeaponRange * 0.8f; // Move to 80% of weapon range
            return unit.transform.position + directionToEnemy * advanceDistance;
        }

        // GeneratePatrolDecision method removed - using existing method below

        // Helper methods removed - using existing TacticalAwareness methods

        /// <summary>
        /// Calculate safe retreat position
        /// </summary>
        private Vector3 CalculateSafeRetreatPosition(Unit unit)
        {
            // Find position away from threats, towards allies
            Vector3 retreatDirection = Vector3.zero;

            // Move away from threats
            foreach (var threat in GetUnitSituation(unit)?.immediateThreats ?? new List<ThreatInfo>())
            {
                Vector3 awayFromThreat = unit.transform.position - threat.threatUnit.transform.position;
                retreatDirection += awayFromThreat.normalized;
            }

            // Move towards allies if available
            var allies = RTSUtilities.FindAlliesInRange(unit, 30f);
            if (allies.Count > 0)
            {
                Vector3 towardsAllies = Vector3.zero;
                foreach (var ally in allies)
                {
                    towardsAllies += ally.transform.position - unit.transform.position;
                }
                retreatDirection += towardsAllies.normalized * 0.5f;
            }

            return unit.transform.position + retreatDirection.normalized * 15f;
        }

        /// <summary>
        /// Calculate tactical positioning relative to threat
        /// </summary>
        private Vector3 CalculateTacticalPosition(Unit unit, Unit threat)
        {
            Vector3 toThreat = threat.transform.position - unit.transform.position;
            Vector3 optimalDistance = toThreat.normalized * unit.WeaponRange * 0.8f; // Stay at 80% of max range

            // Add flanking angle
            Vector3 flankDirection = Vector3.Cross(toThreat.normalized, Vector3.up).normalized;
            Vector3 flankOffset = flankDirection * Random.Range(-5f, 5f);

            return threat.transform.position - optimalDistance + flankOffset;
        }

        private TacticalDecision GenerateCriticalThreatResponse(Unit unit, TacticalSituation situation, float healthPercentage)
        {
            // Critical threat - prioritize survival
            if (healthPercentage < 0.3f)
            {
                // Retreat immediately
                return new TacticalDecision
                {
                    decisionType = TacticalDecisionType.Retreat,
                    targetPosition = CalculateRetreatPosition(unit, situation),
                    priority = 1.0f,
                    reasoning = "Critical health and threat level - immediate retreat required"
                };
            }
            else if (CanCallForReinforcements(unit))
            {
                // Call for help and hold position
                return new TacticalDecision
                {
                    decisionType = TacticalDecisionType.CallReinforcements,
                    targetPosition = unit.transform.position,
                    priority = 0.9f,
                    reasoning = "Critical threat - calling for reinforcements"
                };
            }
            else
            {
                // Fight or retreat based on tactical advantage
                return EvaluateFightOrFlight(unit, situation);
            }
        }

        private TacticalDecision GenerateHighThreatResponse(Unit unit, TacticalSituation situation, float healthPercentage)
        {
            // High threat - tactical maneuvering
            if (situation.immediateThreats.Any(t => t.coverAvailable))
            {
                // Move to cover
                return new TacticalDecision
                {
                    decisionType = TacticalDecisionType.SeekCover,
                    targetPosition = FindBestCoverPosition(unit, situation),
                    priority = 0.8f,
                    reasoning = "High threat - seeking cover"
                };
            }
            else if (CanFlankEnemy(unit, situation))
            {
                // Attempt flanking maneuver
                return new TacticalDecision
                {
                    decisionType = TacticalDecisionType.Flank,
                    targetPosition = CalculateFlankingPosition(unit, situation),
                    priority = 0.7f,
                    reasoning = "High threat - attempting flanking maneuver"
                };
            }
            else
            {
                // Engage primary threat
                return new TacticalDecision
                {
                    decisionType = TacticalDecisionType.Engage,
                    targetUnit = GetPrimaryThreat(situation),
                    priority = 0.6f,
                    reasoning = "High threat - engaging primary target"
                };
            }
        }

        private TacticalDecision GenerateMediumThreatResponse(Unit unit, TacticalSituation situation, float healthPercentage)
        {
            // Medium threat - balanced approach
            if (healthPercentage > 0.7f && CanAdvanceOnEnemy(unit, situation))
            {
                // Advance and engage
                return new TacticalDecision
                {
                    decisionType = TacticalDecisionType.Advance,
                    targetPosition = CalculateAdvancePosition(unit, situation),
                    priority = 0.5f,
                    reasoning = "Medium threat - advancing on enemy"
                };
            }
            else
            {
                // Hold position and engage
                return new TacticalDecision
                {
                    decisionType = TacticalDecisionType.HoldPosition,
                    targetPosition = unit.transform.position,
                    priority = 0.4f,
                    reasoning = "Medium threat - holding position"
                };
            }
        }

        private TacticalDecision GenerateLowThreatResponse(Unit unit, TacticalSituation situation, float healthPercentage)
        {
            // Low threat - proactive actions based on health
            if (situation.immediateThreats.Count > 0)
            {
                // If healthy, investigate; if wounded, be more cautious
                if (healthPercentage > 0.7f)
                {
                    return new TacticalDecision
                    {
                        decisionType = TacticalDecisionType.Investigate,
                        targetPosition = situation.immediateThreats[0].predictedPosition,
                        priority = 0.3f,
                        reasoning = "Low threat, good health - investigating enemy position"
                    };
                }
                else
                {
                    return new TacticalDecision
                    {
                        decisionType = TacticalDecisionType.HoldPosition,
                        targetPosition = unit.transform.position,
                        priority = 0.2f,
                        reasoning = "Low threat, wounded - holding position"
                    };
                }
            }
            else
            {
                return GeneratePatrolDecision(unit, situation);
            }
        }

        private TacticalDecision GeneratePatrolDecision(Unit unit, TacticalSituation situation)
        {
            Vector3 patrolPosition = CalculatePatrolPosition(unit);

            // Adjust patrol based on situation
            if (situation.immediateThreats.Count > 0)
            {
                // Move away from threats while patrolling
                Vector3 threatDirection = Vector3.zero;
                foreach (var threat in situation.immediateThreats)
                {
                    threatDirection += (unit.transform.position - threat.threatUnit.transform.position).normalized;
                }
                patrolPosition += threatDirection.normalized * 5f;
            }

            return new TacticalDecision
            {
                decisionType = TacticalDecisionType.Patrol,
                targetPosition = patrolPosition,
                priority = 0.2f,
                reasoning = "No immediate threats - continuing patrol"
            };
        }



        private bool IsUnitInGroup(Unit unit)
        {
            // Check if unit has nearby allies within group decision radius
            int nearbyAllies = 0;
            foreach (Unit ally in allUnits)
            {
                if (ally != null && ally != unit && ally.Faction == unit.Faction)
                {
                    float distance = Vector3.Distance(unit.transform.position, ally.transform.position);
                    if (distance <= groupDecisionRadius)
                    {
                        nearbyAllies++;
                        if (nearbyAllies >= 2) return true; // Group of 3+ units
                    }
                }
            }
            return false;
        }

        private TacticalDecision GenerateGroupDecisions(Unit unit, TacticalSituation situation)
        {
            // Enhanced decision making for groups with coordination bonus
            TacticalDecision decision = GenerateUnitDecisions(unit, situation);
            if (decision != null)
            {
                // Apply coordination bonus to confidence
                decision.confidence = Mathf.Min(1f, decision.confidence + coordinationBonus);
            }
            return decision;
        }

        private TacticalDecision GenerateUnitDecisions(Unit unit, TacticalSituation situation)
        {
            // Don't make new decisions if unit is already executing one
            if (HasActiveDecision(unit)) return null;

            TacticalDecision decision = null;

            // Decision making based on situation
            switch (situation.threatLevel)
            {
                case ThreatLevel.Critical:
                    decision = DecideOnCriticalThreat(unit, situation);
                    break;
                case ThreatLevel.High:
                    decision = DecideOnHighThreat(unit, situation);
                    break;
                case ThreatLevel.Medium:
                    decision = DecideOnModerateThreat(unit, situation);
                    break;
                case ThreatLevel.Low:
                    decision = DecideOnLowThreat(unit, situation);
                    break;
                case ThreatLevel.None:
                    decision = DecideOnNoThreat(unit, situation);
                    break;
            }

            if (decision != null && decision.confidence >= decisionConfidenceThreshold)
            {
                activeDecisions.Add(decision);
                // Send decision to AI for execution - don't execute twice
                OnTacticalDecisionMade?.Invoke(decision);
            }

            return decision;
        }

        private TacticalDecision DecideOnCriticalThreat(Unit unit, TacticalSituation situation)
        {
            // Critical threat - retreat or call for immediate support
            if (situation.canRetreat && situation.availableSupport < 0.5f)
            {
                return new TacticalDecision
                {
                    unit = unit,
                    decisionType = TacticalDecisionType.Retreat,
                    confidence = 0.9f,
                    priority = 1f,
                    timestamp = Time.time
                };
            }
            else if (situation.availableSupport >= 0.5f)
            {
                return new TacticalDecision
                {
                    unit = unit,
                    decisionType = TacticalDecisionType.CallSupport,
                    confidence = 0.8f,
                    priority = 0.9f,
                    timestamp = Time.time
                };
            }

            return null;
        }

        private TacticalDecision DecideOnHighThreat(Unit unit, TacticalSituation situation)
        {
            // High threat - engage with support or reposition
            if (situation.availableSupport >= 0.3f)
            {
                return new TacticalDecision
                {
                    unit = unit,
                    decisionType = TacticalDecisionType.EngageWithSupport,
                    confidence = 0.7f,
                    priority = 0.8f,
                    timestamp = Time.time
                };
            }
            else if (!situation.hasGoodPosition)
            {
                // Calculate a better position for repositioning
                Vector3 betterPosition = FindBetterPosition(unit, situation);
                return new TacticalDecision
                {
                    unit = unit,
                    decisionType = TacticalDecisionType.Reposition,
                    targetPosition = betterPosition,
                    confidence = 0.6f,
                    priority = 0.7f,
                    timestamp = Time.time
                };
            }

            return null;
        }

        private TacticalDecision DecideOnModerateThreat(Unit unit, TacticalSituation situation)
        {
            // Moderate threat - engage or use flanking
            if (situation.flankingOpportunities.Count > 0)
            {
                return new TacticalDecision
                {
                    unit = unit,
                    decisionType = TacticalDecisionType.Flank,
                    confidence = 0.6f,
                    priority = 0.6f,
                    timestamp = Time.time,
                    targetPosition = situation.flankingOpportunities[0]
                };
            }
            else
            {
                return new TacticalDecision
                {
                    unit = unit,
                    decisionType = TacticalDecisionType.Engage,
                    confidence = 0.5f,
                    priority = 0.5f,
                    timestamp = Time.time
                };
            }
        }

        private TacticalDecision DecideOnLowThreat(Unit unit, TacticalSituation situation)
        {
            Vector3 targetPosition;
            TacticalDecisionType decisionType;

            if (situation.immediateThreats.Count > 0)
            {
                targetPosition = situation.immediateThreats[0].threatUnit.transform.position;
                decisionType = TacticalDecisionType.Investigate;
            }
            else if (situation.nearbyAllies.Count > 0)
            {
                targetPosition = situation.nearbyAllies[0].transform.position;
                decisionType = TacticalDecisionType.SupportAlly;
            }
            else
            {
                targetPosition = unit.transform.position + unit.transform.forward * 10f;
                decisionType = TacticalDecisionType.Advance;
            }

            return new TacticalDecision
            {
                unit = unit,
                decisionType = decisionType,
                targetPosition = targetPosition,
                confidence = 0.4f,
                priority = 0.3f,
                timestamp = Time.time
            };
        }

        private TacticalDecision DecideOnNoThreat(Unit unit, TacticalSituation situation)
        {
            // No immediate threat - patrol or support others
            if (situation.nearbyAllies.Count > 0)
            {
                // Check if any allies need support
                foreach (Unit ally in situation.nearbyAllies)
                {
                    if (unitSituations.ContainsKey(ally) &&
                        unitSituations[ally].threatLevel >= ThreatLevel.Medium)
                    {
                        return new TacticalDecision
                        {
                            unit = unit,
                            decisionType = TacticalDecisionType.SupportAlly,
                            confidence = 0.6f,
                            priority = 0.4f,
                            timestamp = Time.time,
                            targetUnit = ally
                        };
                    }
                }
            }

            return null; // Continue current behavior
        }

        private void ExecuteTacticalDecision(TacticalDecision decision)
        {
            // Apply response delay for realistic decision making
            if (responseDelay > 0)
            {
                StartCoroutine(DelayedTacticalResponse(decision));
            }
            else
            {
                ExecuteTacticalResponse(decision);
            }
        }

        private System.Collections.IEnumerator DelayedTacticalResponse(TacticalDecision decision)
        {
            yield return new WaitForSeconds(responseDelay);
            ExecuteTacticalResponse(decision);
        }

        private void ExecuteTacticalResponse(TacticalDecision decision)
        {
            AI ai = decision.unit.GetComponent<AI>();
            if (ai == null) return;

            switch (decision.decisionType)
            {
                case TacticalDecisionType.Retreat:
                    // AI will handle retreat in its state machine
                    break;

                case TacticalDecisionType.Engage:
                    Unit target = decision.unit.GetClosestEnemy();
                    if (target != null)
                    {
                        ai.EngageTarget(target);
                    }
                    break;

                case TacticalDecisionType.Flank:
                    if (decision.targetPosition != Vector3.zero)
                    {
                        ai.InvestigatePosition(decision.targetPosition);
                    }
                    break;

                case TacticalDecisionType.Reposition:
                    if (unitSituations.ContainsKey(decision.unit))
                    {
                        Vector3 betterPosition = FindBetterPosition(decision.unit, unitSituations[decision.unit]);
                        ai.InvestigatePosition(betterPosition);
                    }
                    break;

                case TacticalDecisionType.SupportAlly:
                    if (decision.targetUnit != null)
                    {
                        ai.InvestigatePosition(decision.targetUnit.transform.position);
                    }
                    break;
            }

            OnTacticalResponseExecuted?.Invoke(decision.unit, new TacticalResponse
            {
                responseType = decision.decisionType,
                timestamp = Time.time
            });
        }

        #endregion

        #region Helper Methods

        private float CalculateThreatScore(Unit observer, Unit threat, float distance)
        {
            if (threat == null) return 0f;

            float baseScore = 1f;

            // Distance factor
            float maxThreatRange = observer.VisionRange;
            float distanceFactor = 1f - (distance / maxThreatRange);

            // Unit type factor
            float typeFactor = GetUnitTypeThreatMultiplier(threat.UnitType, observer.UnitType);

            // Health factor
            float healthFactor = (float)threat.CurrentHealth / threat.MaxHealth;

            return baseScore * distanceFactor * typeFactor * healthFactor;
        }

        private float GetUnitTypeThreatMultiplier(UnitType threatType, UnitType observerType)
        {
            // Simple threat matrix
            if (threatType == UnitType.Tank && observerType == UnitType.Infantry) return 2f;
            if (threatType == UnitType.Artillery) return 1.5f;
            if (threatType == UnitType.Aircraft) return 1.8f;
            return 1f;
        }

        /// <summary>
        /// Calculate advanced threat score with multiple factors
        /// </summary>
        private float CalculateAdvancedThreatScore(Unit unit, Unit enemy, float distance)
        {
            if (unit == null || enemy == null) return 0f;

            float baseThreat = 1f;

            // Distance factor - closer enemies are more threatening
            float distanceFactor = Mathf.Clamp01(1f - (distance / 30f));

            // Health factor - wounded units are less threatening
            float healthFactor = (float)enemy.CurrentHealth / enemy.MaxHealth;

            // Unit type effectiveness
            float typeFactor = GetUnitTypeEffectiveness(enemy.UnitType, unit.UnitType);

            // Weapon range factor - enemies with longer range are more threatening
            float weaponRangeFactor = enemy.GetWeaponRange() / 20f;

            // Line of sight factor - enemies with clear shots are more dangerous
            Vector3 enemyPos = enemy.transform.position + Vector3.up * 0.5f;
            Vector3 unitPos = unit.transform.position + Vector3.up * 0.5f;
            float losFactor = HasLineOfSight(enemyPos, unitPos) ? 1.2f : 0.8f;

            // Cover factor - enemies in cover are more threatening
            float coverFactor = IsInCover(enemy) ? 1.1f : 1f;

            // Calculate final threat score
            float threatScore = baseThreat * distanceFactor * healthFactor * typeFactor *
                              weaponRangeFactor * losFactor * coverFactor;

            return Mathf.Clamp01(threatScore);
        }

        /// <summary>
        /// Determine the type of threat an enemy represents
        /// </summary>
        private ThreatType DetermineThreatType(Unit enemy)
        {
            if (enemy == null) return ThreatType.Unknown;

            switch (enemy.UnitType)
            {
                case UnitType.Infantry:
                    return ThreatType.Infantry;
                case UnitType.Tank:
                case UnitType.HeavyVehicle:
                    return ThreatType.Armor;
                case UnitType.Aircraft:
                    return ThreatType.Air;
                case UnitType.Building:
                    return ThreatType.Structure;
                default:
                    return ThreatType.Unknown;
            }
        }

        /// <summary>
        /// Assess if cover is available between unit and enemy
        /// </summary>
        private bool AssessCoverAvailability(Unit unit, Unit enemy)
        {
            if (unit == null || enemy == null) return false;

            Vector3 directionToEnemy = (enemy.transform.position - unit.transform.position).normalized;
            Vector3 perpendicular = Vector3.Cross(directionToEnemy, Vector3.up);

            // Check for cover positions to the left and right
            float coverCheckDistance = 5f;
            Vector3[] coverPositions = {
                unit.transform.position + perpendicular * coverCheckDistance,
                unit.transform.position - perpendicular * coverCheckDistance
            };

            foreach (Vector3 coverPos in coverPositions)
            {
                if (Physics.Raycast(coverPos, directionToEnemy, out RaycastHit hit,
                    Vector3.Distance(unit.transform.position, enemy.transform.position)))
                {
                    // Found potential cover
                    if (hit.collider.gameObject != enemy.gameObject)
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        /// <summary>
        /// Predict where an enemy will be in the future
        /// </summary>
        private Vector3 PredictEnemyPosition(Unit enemy, float timeAhead)
        {
            if (enemy == null) return Vector3.zero;

            Rigidbody enemyRb = enemy.GetComponent<Rigidbody>();
            if (enemyRb != null && enemyRb.linearVelocity.magnitude > 0.1f)
            {
                // Predict based on current velocity
                return enemy.transform.position + enemyRb.linearVelocity * timeAhead;
            }

            // If no movement, return current position
            return enemy.transform.position;
        }



        /// <summary>
        /// Check if unit is in cover
        /// </summary>
        private bool IsInCover(Unit unit)
        {
            if (unit == null) return false;

            // Simple cover check - look for nearby obstacles
            Collider[] nearbyObjects = Physics.OverlapSphere(unit.transform.position, 2f);

            foreach (Collider obj in nearbyObjects)
            {
                if (obj.gameObject != unit.gameObject &&
                    obj.bounds.size.y > 1f) // Tall enough to provide cover
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// Get unit type effectiveness multiplier
        /// </summary>
        private float GetUnitTypeEffectiveness(UnitType attackerType, UnitType targetType)
        {
            // Simplified effectiveness matrix
            switch (attackerType)
            {
                case UnitType.Infantry:
                    return targetType == UnitType.Tank ? 0.5f : 1f;
                case UnitType.Tank:
                    return targetType == UnitType.Infantry ? 1.5f : 1f;
                case UnitType.Aircraft:
                    return 1.2f; // Aircraft generally effective
                default:
                    return 1f;
            }
        }

        private bool EvaluatePosition(Unit unit)
        {
            if (unit == null) return false;

            // Check if unit has good cover nearby
            Collider[] nearbyObjects = Physics.OverlapSphere(unit.transform.position, 5f);
            bool hasCover = false;

            foreach (Collider obj in nearbyObjects)
            {
                if (obj.gameObject != unit.gameObject && obj.bounds.size.y > 1f)
                {
                    hasCover = true;
                    break;
                }
            }

            // Check if position is on high ground
            bool hasHeightAdvantage = false;
            if (Physics.Raycast(unit.transform.position, Vector3.down, out RaycastHit hit, 10f))
            {
                // Simple height check - could be enhanced
                hasHeightAdvantage = hit.point.y > unit.transform.position.y - 2f;
            }

            return hasCover || hasHeightAdvantage;
        }

        private bool CanRetreatSafely(Unit unit)
        {
            if (unit == null) return false;

            // Check for enemies that might flank during retreat
            Vector3 retreatDirection = CalculateRetreatPosition(unit, unitSituations[unit]) - unit.transform.position;
            retreatDirection = retreatDirection.normalized;

            // Check flanking positions (90 degrees left and right of retreat direction)
            Vector3 leftFlank = Quaternion.Euler(0, -90, 0) * retreatDirection;
            Vector3 rightFlank = Quaternion.Euler(0, 90, 0) * retreatDirection;

            // Check for enemies in flanking positions
            foreach (Unit enemy in unit.DetectedEnemies)
            {
                if (enemy == null) continue;

                Vector3 toEnemy = (enemy.transform.position - unit.transform.position).normalized;
                float leftDot = Vector3.Dot(toEnemy, leftFlank);
                float rightDot = Vector3.Dot(toEnemy, rightFlank);

                // If enemy is in flanking position and close enough
                if ((leftDot > 0.7f || rightDot > 0.7f) &&
                    Vector3.Distance(unit.transform.position, enemy.transform.position) < 15f)
                {
                    return false; // Not safe to retreat
                }
            }

            return true; // Safe to retreat
        }

        #region Advanced Tactical Decision Helpers

        private Vector3 CalculateRetreatPosition(Unit unit, TacticalSituation situation)
        {
            if (unit == null) return Vector3.zero;

            // Find direction away from threats
            Vector3 retreatDirection = Vector3.zero;
            foreach (var threat in situation.immediateThreats)
            {
                Vector3 awayFromThreat = (unit.transform.position - threat.threatUnit.transform.position).normalized;
                retreatDirection += awayFromThreat * threat.threatScore;
            }

            retreatDirection = retreatDirection.normalized;
            Vector3 retreatPosition = unit.transform.position + retreatDirection * 15f;

            // Ensure valid position
            if (PathfindingManager.Instance != null)
            {
                retreatPosition = PathfindingManager.Instance.GetNearestValidPosition(retreatPosition);
            }

            return retreatPosition;
        }

        private bool CanCallForReinforcements(Unit unit)
        {
            // Check if unit can communicate and hasn't recently called for help
            return IntelligenceNetwork.Instance != null &&
                   Time.time - unit.GetComponent<AI>()?.LastReinforcementCall > 30f;
        }

        private TacticalDecision EvaluateFightOrFlight(Unit unit, TacticalSituation situation)
        {
            float combatPower = CalculateCombatPower(unit);
            float enemyCombatPower = situation.immediateThreats.Sum(t => CalculateCombatPower(t.threatUnit));

            if (combatPower > enemyCombatPower * 0.8f)
            {
                // Fight
                return new TacticalDecision
                {
                    decisionType = TacticalDecisionType.Engage,
                    targetUnit = GetPrimaryThreat(situation),
                    priority = 0.8f,
                    reasoning = "Combat power advantage - engaging"
                };
            }
            else
            {
                // Flight
                return new TacticalDecision
                {
                    decisionType = TacticalDecisionType.Retreat,
                    targetPosition = CalculateRetreatPosition(unit, situation),
                    priority = 0.9f,
                    reasoning = "Combat power disadvantage - retreating"
                };
            }
        }

        private Vector3 FindBestCoverPosition(Unit unit, TacticalSituation situation)
        {
            Vector3 bestCover = unit.transform.position;
            float bestCoverScore = 0f;

            // Search for cover positions in a radius
            for (int i = 0; i < 8; i++)
            {
                float angle = i * 45f * Mathf.Deg2Rad;
                Vector3 testPosition = unit.transform.position + new Vector3(Mathf.Cos(angle), 0, Mathf.Sin(angle)) * 5f;

                float coverScore = EvaluateCoverPosition(testPosition, situation);
                if (coverScore > bestCoverScore)
                {
                    bestCoverScore = coverScore;
                    bestCover = testPosition;
                }
            }

            return bestCover;
        }

        private float EvaluateCoverPosition(Vector3 position, TacticalSituation situation)
        {
            float coverScore = 0f;

            // Check how many threats this position provides cover from
            foreach (var threat in situation.immediateThreats)
            {
                Vector3 directionToThreat = (threat.threatUnit.transform.position - position).normalized;
                if (Physics.Raycast(position, directionToThreat, out RaycastHit hit,
                    Vector3.Distance(position, threat.threatUnit.transform.position)))
                {
                    if (hit.collider.gameObject != threat.threatUnit.gameObject)
                    {
                        coverScore += threat.threatScore;
                    }
                }
            }

            return coverScore;
        }

        private bool CanFlankEnemy(Unit unit, TacticalSituation situation)
        {
            if (situation.immediateThreats.Count == 0) return false;

            Unit primaryThreat = GetPrimaryThreat(situation);
            Vector3 flankPosition = CalculateFlankingPosition(unit, situation);

            // Check if flanking position is accessible and provides advantage
            return Vector3.Distance(unit.transform.position, flankPosition) < 20f &&
                   PathfindingManager.Instance?.IsValidDestination(flankPosition) == true;
        }

        private Vector3 CalculateFlankingPosition(Unit unit, TacticalSituation situation)
        {
            if (situation.immediateThreats.Count == 0) return unit.transform.position;

            Unit primaryThreat = GetPrimaryThreat(situation);
            Vector3 threatForward = primaryThreat.transform.forward;
            Vector3 threatRight = primaryThreat.transform.right;

            // Position to the side and slightly behind the threat
            Vector3 flankPosition = primaryThreat.transform.position + threatRight * 10f - threatForward * 5f;

            return flankPosition;
        }

        private Unit GetPrimaryThreat(TacticalSituation situation)
        {
            if (situation.immediateThreats.Count == 0) return null;

            return situation.immediateThreats
                .OrderByDescending(t => t.threatScore)
                .First().threatUnit;
        }

        private bool CanAdvanceOnEnemy(Unit unit, TacticalSituation situation)
        {
            if (situation.immediateThreats.Count == 0) return false;

            float combatPower = CalculateCombatPower(unit);
            float enemyCombatPower = situation.immediateThreats.Sum(t => CalculateCombatPower(t.threatUnit));

            return combatPower > enemyCombatPower * 1.2f; // Need significant advantage to advance
        }

        private Vector3 CalculateAdvancePosition(Unit unit, TacticalSituation situation)
        {
            if (situation.immediateThreats.Count == 0) return unit.transform.position;

            Unit primaryThreat = GetPrimaryThreat(situation);
            Vector3 directionToThreat = (primaryThreat.transform.position - unit.transform.position).normalized;

            // Advance to optimal weapon range
            float optimalRange = unit.GetWeaponRange() * 0.8f;
            Vector3 advancePosition = primaryThreat.transform.position - directionToThreat * optimalRange;

            return advancePosition;
        }

        private Vector3 CalculatePatrolPosition(Unit unit)
        {
            // Simple patrol - move in a random direction
            Vector3 randomDirection = new Vector3(
                UnityEngine.Random.Range(-1f, 1f),
                0,
                UnityEngine.Random.Range(-1f, 1f)
            ).normalized;

            return unit.transform.position + randomDirection * 10f;
        }

        private float CalculateCombatPower(Unit unit)
        {
            return RTSUtilities.CalculateCombatPower(unit);
        }

        #endregion

        private List<Vector3> FindFlankingOpportunities(Unit unit)
        {
            List<Vector3> opportunities = new List<Vector3>();

            foreach (Unit enemy in unit.DetectedEnemies)
            {
                if (enemy == null) continue;

                // Calculate potential flanking positions
                Vector3 enemyPos = enemy.transform.position;
                Vector3 flankLeft = enemyPos + enemy.transform.right * -10f;
                Vector3 flankRight = enemyPos + enemy.transform.right * 10f;

                opportunities.Add(flankLeft);
                opportunities.Add(flankRight);
            }

            return opportunities;
        }

        private float CalculateSupportValue(Unit ally, float distance)
        {
            float baseValue = 1f;
            float distanceFactor = 1f - (distance / situationAssessmentRange);

            // Factor in ally's combat capability
            float combatFactor = ally.CanAttack ? 1f : 0.5f;

            return baseValue * distanceFactor * combatFactor;
        }



        private bool HasActiveDecision(Unit unit)
        {
            return activeDecisions.Exists(d => d.unit == unit && Time.time - d.timestamp < 5f);
        }

        private void ProcessTacticalDecisions()
        {
            // Clean up old decisions
            activeDecisions.RemoveAll(d => Time.time - d.timestamp > 10f);
        }

        private void UpdateFactionAwareness(Faction faction)
        {
            if (!factionAwareness.ContainsKey(faction)) return;

            FactionAwareness awareness = factionAwareness[faction];

            // Update based on unit situations
            List<Unit> factionUnits = GetFactionUnits(faction);
            float totalThreat = 0f;

            foreach (Unit unit in factionUnits)
            {
                if (unitSituations.ContainsKey(unit))
                {
                    TacticalSituation situation = unitSituations[unit];
                    totalThreat += (float)situation.threatLevel / 4f; // Normalize threat level
                }
            }

            if (factionUnits.Count > 0)
            {
                totalThreat /= factionUnits.Count;
            }

            // Update overall threat level
            awareness.overallThreatLevel = (ThreatLevel)Mathf.RoundToInt(totalThreat * 4f);
        }

        private List<Unit> GetFactionUnits(Faction faction)
        {
            return RTSUtilities.GetFactionUnits(faction);
        }

        private void RefreshUnitList()
        {
            RTSUtilities.RefreshUnitList(allUnits);
            playerUnits.Clear();

            // Categorize player units for fog of war
            foreach (Unit unit in allUnits)
            {
                if (unit.Faction == Faction.Player)
                {
                    playerUnits.Add(unit);
                }
            }
        }

        private void SubscribeToEvents()
        {
            // Subscribe to relevant events from other systems
            if (IntelligenceNetwork.Instance != null)
            {
                IntelligenceNetwork.Instance.OnIntelReportCreated += OnIntelReportReceived;
            }

            // Subscribe to combat events for tactical analysis
            if (CombatSystem.Instance != null)
            {
                CombatSystem.Instance.OnDamageDealt += OnCombatEvent;
                CombatSystem.Instance.OnUnitKilled += OnUnitKilled;
            }
        }

        /// <summary>
        /// Handle combat events for tactical analysis
        /// </summary>
        private void OnCombatEvent(Unit attacker, Unit target, int damage)
        {
            // Force tactical reassessment for both units
            ForceAssessment(attacker);
            ForceAssessment(target);
        }

        /// <summary>
        /// Handle unit death for tactical analysis
        /// </summary>
        private void OnUnitKilled(Unit killedUnit)
        {
            // Remove from tactical tracking
            if (unitSituations.ContainsKey(killedUnit))
            {
                unitSituations.Remove(killedUnit);
            }
        }

        private void OnIntelReportReceived(IntelReport report)
        {
            if (report == null) return;

            // Process intelligence reports to update awareness
            if (report.intelType == IntelType.EnemySighting && report.subjectUnit != null)
            {
                // Update faction awareness with new enemy sighting
                if (factionAwareness.ContainsKey(report.reporterFaction))
                {
                    var awareness = factionAwareness[report.reporterFaction];
                    if (!awareness.knownEnemies.Contains(report.subjectUnit))
                    {
                        awareness.knownEnemies.Add(report.subjectUnit);
                    }
                }
            }

            // Could trigger immediate tactical responses based on report priority
            if (report.priority > 0.8f && enableAutomaticResponses)
            {
                // High priority reports might trigger immediate responses
                foreach (Unit unit in allUnits)
                {
                    if (unit != null && unit.Faction == report.reporterFaction && unit.Faction != Faction.Player)
                    {
                        // Only non-player units respond to urgent reports automatically
                        if (Vector3.Distance(unit.transform.position, report.position) < 20f)
                        {
                            // Close units investigate urgent reports
                            unit.MoveTo(report.position);
                        }
                    }
                }
            }
        }

        #endregion

        // Player Unit Basic Combat section removed - all units now use unified targeting system in Unit.cs

        #region Combat Autonomy

        private void ProcessCombatAutonomy()
        {
            foreach (Unit unit in allUnits)
            {
                if (unit == null || !unit.gameObject.activeInHierarchy || unit.IsDead) continue;
                if (unit.Faction == Faction.Player) continue; // Don't control player units

                ProcessUnitAutonomy(unit);
            }
        }

        private void ProcessUnitAutonomy(Unit unit)
        {
            if (!unitSituations.ContainsKey(unit)) return;

            TacticalSituation situation = unitSituations[unit];
            float healthPercentage = (float)unit.CurrentHealth / unit.MaxHealth;

            // Check if unit should retreat
            if (healthPercentage <= retreatHealthThreshold && situation.canRetreat)
            {
                ExecuteRetreat(unit, situation);
                return;
            }

            // Check if unit should reposition for better tactical advantage
            if (ShouldReposition(unit, situation))
            {
                ExecuteReposition(unit, situation);
                return;
            }

            // Check for suppression opportunities
            if (enableSuppressionSystem && ShouldSuppressEnemies(unit, situation))
            {
                ExecuteSuppression(unit, situation);
            }
        }

        private bool ShouldReposition(Unit unit, TacticalSituation situation)
        {
            // Don't reposition too frequently
            if (lastRepositionTime.ContainsKey(unit) &&
                Time.time - lastRepositionTime[unit] < repositionCooldown)
            {
                return false;
            }

            // Reposition if in poor tactical position or under heavy threat
            return !situation.hasGoodPosition ||
                   situation.threatLevel >= ThreatLevel.High ||
                   situation.flankingOpportunities.Count > 0;
        }

        private void ExecuteRetreat(Unit unit, TacticalSituation situation)
        {
            Vector3 retreatPosition = FindRetreatPosition(unit, situation);
            if (retreatPosition != Vector3.zero)
            {
                // Move to retreat position
                if (unit.NavAgent != null)
                {
                    unit.NavAgent.SetDestination(retreatPosition);
                }

                // Clear current target to stop attacking
                unit.SetTarget(null);

                lastRepositionTime[unit] = Time.time;
                Debug.Log($"{unit.name} retreating to {retreatPosition}");
            }
        }

        private void ExecuteReposition(Unit unit, TacticalSituation situation)
        {
            Vector3 newPosition = FindBetterPosition(unit, situation);
            if (newPosition != Vector3.zero)
            {
                if (unit.NavAgent != null)
                {
                    unit.NavAgent.SetDestination(newPosition);
                }

                lastRepositionTime[unit] = Time.time;

            }
        }

        private void ExecuteSuppression(Unit unit, TacticalSituation situation)
        {
            // Find best target for suppression (closest or most threatening)
            Unit suppressionTarget = FindSuppressionTarget(unit, situation);
            if (suppressionTarget != null)
            {
                unit.SetTarget(suppressionTarget);

            }
        }

        private Vector3 FindRetreatPosition(Unit unit, TacticalSituation situation)
        {
            Vector3 unitPos = unit.transform.position;
            Vector3 threatDirection = Vector3.zero;

            // Calculate average threat direction
            foreach (var threat in situation.immediateThreats)
            {
                if (threat.threatUnit != null)
                {
                    threatDirection += (threat.threatUnit.transform.position - unitPos).normalized;
                }
            }

            if (threatDirection != Vector3.zero)
            {
                threatDirection.Normalize();
                // Retreat in opposite direction
                Vector3 retreatDirection = -threatDirection;

                // Look for cover or allies in retreat direction
                Vector3 retreatPos = unitPos + retreatDirection * coverSeekingRange;

                // Use NavMesh to find valid position
                if (UnityEngine.AI.NavMesh.SamplePosition(retreatPos, out UnityEngine.AI.NavMeshHit hit, coverSeekingRange, -1))
                {
                    return hit.position;
                }
            }

            return Vector3.zero;
        }

        private Vector3 FindBetterPosition(Unit unit, TacticalSituation situation)
        {
            Vector3 unitPos = unit.transform.position;
            Vector3 bestPosition = Vector3.zero;
            float bestScore = 0f;
            float weaponRange = unit.GetWeaponRange();
            float optimalRange = weaponRange * 0.8f; // Prefer 80% of max range

            // Check flanking opportunities first, but respect weapon range
            foreach (Vector3 flankPos in situation.flankingOpportunities)
            {
                // Only consider flanking positions that maintain good range to primary threats
                bool goodRangeToThreats = true;
                foreach (var threat in situation.immediateThreats)
                {
                    float distanceToThreat = Vector3.Distance(flankPos, threat.threatUnit.transform.position);
                    if (distanceToThreat < optimalRange * 0.5f || distanceToThreat > weaponRange)
                    {
                        goodRangeToThreats = false;
                        break;
                    }
                }

                if (goodRangeToThreats)
                {
                    float score = EvaluatePositionScore(unit, flankPos, situation);
                    if (score > bestScore)
                    {
                        bestScore = score;
                        bestPosition = flankPos;
                    }
                }
            }

            // If no good flanking positions, look for positions that maintain optimal range
            if (bestPosition == Vector3.zero)
            {
                bestPosition = FindOptimalRangePosition(unit, situation, optimalRange);
            }

            // Final fallback - if still no position found, move to a random nearby position
            if (bestPosition == Vector3.zero)
            {
                Vector2 randomOffset = Random.insideUnitCircle * 10f;
                bestPosition = unitPos + new Vector3(randomOffset.x, 0, randomOffset.y);

                // Ensure the position is on the NavMesh
                if (UnityEngine.AI.NavMesh.SamplePosition(bestPosition, out UnityEngine.AI.NavMeshHit hit, 10f, UnityEngine.AI.NavMesh.AllAreas))
                {
                    bestPosition = hit.position;
                }
                else
                {
                    bestPosition = unitPos; // Stay in place if no valid position found
                }
            }

            return bestPosition;
        }

        private Vector3 FindHighGroundPosition(Unit unit, TacticalSituation situation)
        {
            Vector3 unitPos = unit.transform.position;
            Vector3 bestPosition = Vector3.zero;
            float bestHeight = unitPos.y;

            // Search in a radius around the unit
            for (int i = 0; i < 8; i++)
            {
                float angle = i * 45f * Mathf.Deg2Rad;
                Vector3 searchDir = new Vector3(Mathf.Cos(angle), 0, Mathf.Sin(angle));
                Vector3 searchPos = unitPos + searchDir * coverSeekingRange;

                if (UnityEngine.AI.NavMesh.SamplePosition(searchPos, out UnityEngine.AI.NavMeshHit hit, 5f, -1))
                {
                    if (hit.position.y > bestHeight + 2f) // Significant height advantage
                    {
                        bestHeight = hit.position.y;
                        bestPosition = hit.position;
                    }
                }
            }

            return bestPosition;
        }

        private Vector3 FindOptimalRangePosition(Unit unit, TacticalSituation situation, float optimalRange)
        {
            Vector3 unitPos = unit.transform.position;

            // If we have immediate threats, try to maintain optimal range to the closest one
            if (situation.immediateThreats.Count > 0)
            {
                Unit primaryThreat = situation.immediateThreats[0].threatUnit;
                Vector3 threatPos = primaryThreat.transform.position;
                float currentDistance = Vector3.Distance(unitPos, threatPos);

                // If we're too close, back away
                if (currentDistance < optimalRange * 0.7f)
                {
                    Vector3 directionAway = (unitPos - threatPos).normalized;
                    return unitPos + directionAway * 5f; // Move 5 units away
                }
                // If we're too far, move closer but not too close
                else if (currentDistance > optimalRange * 1.2f)
                {
                    Vector3 directionToward = (threatPos - unitPos).normalized;
                    return unitPos + directionToward * 3f; // Move 3 units closer
                }
            }

            // If no immediate threats or range is good, look for high ground or cover
            return FindHighGroundPosition(unit, situation);
        }

        private float EvaluatePositionScore(Unit unit, Vector3 position, TacticalSituation situation)
        {
            float score = 0f;

            // Height advantage
            if (position.y > unit.transform.position.y)
            {
                score += (position.y - unit.transform.position.y) * 2f;
            }

            // Distance from threats
            foreach (var threat in situation.immediateThreats)
            {
                if (threat.threatUnit != null)
                {
                    float distance = Vector3.Distance(position, threat.threatUnit.transform.position);
                    score += distance * 0.1f; // Prefer positions farther from threats
                }
            }

            // Proximity to allies
            foreach (Unit ally in situation.nearbyAllies)
            {
                if (ally != null)
                {
                    float distance = Vector3.Distance(position, ally.transform.position);
                    if (distance < 10f) // Close support
                    {
                        score += 5f - distance * 0.5f;
                    }
                }
            }

            return score;
        }

        private bool ShouldSuppressEnemies(Unit unit, TacticalSituation situation)
        {
            // Suppress if we have tactical advantage and enemies in range
            return situation.overallScore > 0.6f &&
                   situation.immediateThreats.Count > 0 &&
                   unit.CanAttack;
        }

        private Unit FindSuppressionTarget(Unit unit, TacticalSituation situation)
        {
            Unit bestTarget = null;
            float bestScore = 0f;

            foreach (var threat in situation.immediateThreats)
            {
                if (threat.threatUnit != null && !threat.threatUnit.IsDead)
                {
                    float distance = Vector3.Distance(unit.transform.position, threat.threatUnit.transform.position);
                    if (distance <= unit.GetWeaponRange())
                    {
                        // Prioritize closer, more threatening targets
                        float score = threat.threatScore / (distance + 1f);
                        if (score > bestScore)
                        {
                            bestScore = score;
                            bestTarget = threat.threatUnit;
                        }
                    }
                }
            }

            return bestTarget;
        }

        #endregion

        #region Public Interface

        /// <summary>
        /// Get the tactical situation for a specific unit
        /// </summary>
        public TacticalSituation GetUnitSituation(Unit unit)
        {
            return unitSituations.ContainsKey(unit) ? unitSituations[unit] : null;
        }

        /// <summary>
        /// Get faction-level awareness
        /// </summary>
        public FactionAwareness GetFactionAwareness(Faction faction)
        {
            return factionAwareness.ContainsKey(faction) ? factionAwareness[faction] : null;
        }

        /// <summary>
        /// Force a tactical assessment for a unit
        /// </summary>
        public void ForceAssessment(Unit unit)
        {
            if (unit != null)
            {
                UpdateUnitSituation(unit);
            }
        }

        /// <summary>
        /// Get all units visible to a specific unit
        /// </summary>
        public List<Unit> GetVisibleUnits(Unit observer)
        {
            if (unitVisionData.ContainsKey(observer))
            {
                return new List<Unit>(unitVisionData[observer].visibleUnits);
            }
            return new List<Unit>();
        }

        /// <summary>
        /// Check if one unit can see another
        /// </summary>
        public bool CanSeeUnit(Unit observer, Unit target)
        {
            if (unitVisionData.ContainsKey(observer))
            {
                return unitVisionData[observer].visibleUnits.Contains(target);
            }
            return false;
        }

        /// <summary>
        /// Get the fog of war texture for UI display
        /// </summary>
        public Texture2D GetFogOfWarTexture()
        {
            return fogTexture;
        }

        #endregion
    }

    /// <summary>
    /// Vision data for a unit
    /// </summary>
    [System.Serializable]
    public class VisionData
    {
        public List<Unit> visibleUnits = new List<Unit>();
        public float lastVisionUpdate;
    }

    /// <summary>
    /// Tactical situation assessment for a unit
    /// </summary>
    [System.Serializable]
    public class TacticalSituation
    {
        public Unit unit;
        public ThreatLevel threatLevel;
        public List<ThreatInfo> immediateThreats = new List<ThreatInfo>();
        public List<Unit> nearbyAllies = new List<Unit>();
        public bool hasGoodPosition;
        public bool hasPositionalAdvantage;
        public bool canRetreat;
        public float availableSupport;
        public List<Vector3> flankingOpportunities = new List<Vector3>();
        public float overallScore;
        public float lastUpdate;
    }

    /// <summary>
    /// Information about a specific threat
    /// </summary>
    [System.Serializable]
    public class ThreatInfo
    {
        public Unit threatUnit;
        public float threatScore;
        public float distance;
        public float lastSeen;
        public float predictedThreatScore;
        public ThreatType threatType;
        public bool coverAvailable;
        public Vector3 predictedPosition;
    }

    /// <summary>
    /// Types of threats that can be identified
    /// </summary>
    public enum ThreatType
    {
        Unknown,
        Infantry,
        Armor,
        Air,
        Structure,
        Artillery
    }

    /// <summary>
    /// Faction-level tactical awareness
    /// </summary>
    [System.Serializable]
    public class FactionAwareness
    {
        public Faction faction;
        public ThreatLevel overallThreatLevel;
        public List<Unit> knownEnemies = new List<Unit>();
        public float tacticalAdvantage;
        public float lastUpdate;
    }

    /// <summary>
    /// Tactical decision made by the AI
    /// </summary>
    [System.Serializable]
    public class TacticalDecision
    {
        public Unit unit;
        public TacticalDecisionType decisionType;
        public float confidence;
        public float priority;
        public float timestamp;
        public Vector3 targetPosition;
        public Unit targetUnit;
        public string reasoning;
    }

    /// <summary>
    /// Tactical response executed by a unit
    /// </summary>
    [System.Serializable]
    public class TacticalResponse
    {
        public TacticalDecisionType responseType;
        public float timestamp;
        public bool successful;
    }

    /// <summary>
    /// Types of tactical decisions
    /// </summary>
    public enum TacticalDecisionType
    {
        Engage,             // Attack enemy
        Retreat,            // Withdraw from combat
        Flank,              // Attempt flanking maneuver
        Reposition,         // Move to better position
        CallSupport,        // Request reinforcements
        CallReinforcements, // Call for backup
        EngageWithSupport,  // Attack with allied support
        SupportAlly,        // Assist friendly unit
        Advance,            // Move forward
        Defend,             // Hold position
        HoldPosition,       // Maintain current position
        SeekCover,          // Find protective cover
        Investigate,        // Check area of interest
        Patrol              // Continue patrol route
    }
}